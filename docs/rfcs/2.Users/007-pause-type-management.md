# RFC-007: Pause Type Management System

## Özet
Bu RFC, MasterCRM sisteminde çalışanların farklı türde molalar alabilmesi için Pause Type (Mola Türü) yönetim sisteminin tasarımını ve implementasyonunu tanımlar. Sistem, mola türlerinin dinamik olarak tanımlanması, maksimum süre limitlerinin belirlenmesi ve aktif/pasif durumlarının yönetilmesini sağlar.

## Motivasyon
- Çalışanların farklı mola türleri (kahve molası, öğle yemeği, toplantı, vb.) alabilmesi
- Her mola türü için maksimum süre limitlerinin belirlenmesi
- Mola türlerinin dinamik olarak yönetilebilmesi
- İnsan kaynakları departmanının mola politikalarını sistem üzerinden kontrol edebilmesi
- Çalışan performans raporlarında mola analizi yapılabilmesi

## Teknik Tasarım

### Entity Tanımı

```csharp
public class PauseType : BaseEntity
{
    public string Name { get; set; }
    public int MaxDuration { get; set; }
    public bool Active { get; set; }
}
```

### Klasör Yapısı
```
src/
├── Modules/
│   └── Users/
│       ├── Application/
│       │   ├── PauseTypes/
│       │   │   ├── CreatePauseType/
│       │   │   │   ├── CreatePauseTypeCommand.cs
│       │   │   │   ├── CreatePauseTypeCommandHandler.cs
│       │   │   │   ├── CreatePauseTypeCommandValidator.cs
│       │   │   │   └── CreatePauseTypeEndpoint.cs
│       │   │   ├── GetPauseType/
│       │   │   │   ├── GetPauseTypeQuery.cs
│       │   │   │   ├── GetPauseTypeQueryHandler.cs
│       │   │   │   └── GetPauseTypeEndpoint.cs
│       │   │   ├── UpdatePauseType/
│       │   │   │   ├── UpdatePauseTypeCommand.cs
│       │   │   │   ├── UpdatePauseTypeCommandHandler.cs
│       │   │   │   ├── UpdatePauseTypeCommandValidator.cs
│       │   │   │   └── UpdatePauseTypeEndpoint.cs
│       │   │   ├── DeletePauseType/
│       │   │   │   ├── DeletePauseTypeCommand.cs
│       │   │   │   ├── DeletePauseTypeCommandHandler.cs
│       │   │   │   └── DeletePauseTypeEndpoint.cs
│       │       └── ListPauseTypes/
│       └── Infrastructure/
│           └── Data/
│               ├── PauseTypeEntityConfiguration.cs
│               └── PauseTypeRepository.cs
```

### Veritabanı Şeması

```sql
CREATE TABLE PauseTypes.PauseType (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Name NVARCHAR(100) NOT NULL,
    MaxDuration INT NOT NULL,
    Active BIT NOT NULL DEFAULT 1,
    CONSTRAINT UK_PauseType_Name UNIQUE (Name)
);
```

### CQRS Implementation

#### Command Örneği - CreatePauseType

```csharp
// CreatePauseTypeCommand.cs
public record CreatePauseTypeCommand(
    string Name,
    int MaxDuration,
    bool Active = true
) : IRequest<Result<Guid>>;

// CreatePauseTypeCommandHandler.cs
public class CreatePauseTypeCommandHandler : IRequestHandler<CreatePauseTypeCommand, Result<Guid>>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;

    public CreatePauseTypeCommandHandler(
        IApplicationDbContext context,
        ICurrentUserService currentUserService)
    {
        _context = context;
        _currentUserService = currentUserService;
    }

    public async Task<Result<Guid>> Handle(CreatePauseTypeCommand request, CancellationToken cancellationToken)
    {
        var pauseType = new PauseType
        {
            Id = Guid.NewGuid(),
            Name = request.Name,
            MaxDuration = request.MaxDuration,
            Active = request.Active,
            InsertDate = DateTime.UtcNow,
            InsertUserId = _currentUserService.UserId
        };

        _context.PauseTypes.Add(pauseType);
        await _context.SaveChangesAsync(cancellationToken);

        return Result.Success(pauseType.Id);
    }
}

// CreatePauseTypeCommandValidator.cs
public class CreatePauseTypeCommandValidator : AbstractValidator<CreatePauseTypeCommand>
{
    public CreatePauseTypeCommandValidator()
    {
        RuleFor(x => x.Name)
            .NotEmpty()
            .WithMessage("Mola türü adı zorunludur.")
            .MaximumLength(100)
            .WithMessage("Mola türü adı en fazla 100 karakter olabilir.");

        RuleFor(x => x.MaxDuration)
            .GreaterThan(0)
            .WithMessage("Maksimum süre 0'dan büyük olmalıdır.")
            .LessThanOrEqualTo(28800) // 8 saat
            .WithMessage("Maksimum süre 8 saati geçemez.");
    }
}

// CreatePauseTypeEndpoint.cs
public class CreatePauseTypeEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("/api/v1/pausetypes", async (CreatePauseTypeCommand command, IMediator mediator) =>
        {
            var result = await mediator.Send(command);
            return result.IsSuccess ? Results.Ok(result) : Results.BadRequest(result);
        })
        .WithTags("PauseTypes")
        .RequireAuthorization();
    }
}
```

#### Query Örneği - GetPauseType

```csharp
// GetPauseTypeQuery.cs
public record GetPauseTypeQuery(Guid Id) : IRequest<Result<PauseTypeDto>>;

public record PauseTypeDto(
    Guid Id,
    string Name,
    int MaxDuration,
    bool Active,
    DateTime InsertDate,
    DateTime? UpdateDate
);

// GetPauseTypeQueryHandler.cs
public class GetPauseTypeQueryHandler : IRequestHandler<GetPauseTypeQuery, Result<PauseTypeDto>>
{
    private readonly IApplicationDbContext _context;

    public GetPauseTypeQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<PauseTypeDto>> Handle(GetPauseTypeQuery request, CancellationToken cancellationToken)
    {
        var pauseType = await _context.PauseTypes
            .Where(x => x.Id == request.Id)
            .Select(x => new PauseTypeDto(
                x.Id,
                x.Name,
                x.MaxDuration,
                x.Active,
                x.InsertDate,
                x.UpdateDate
            ))
            .FirstOrDefaultAsync(cancellationToken);

        if (pauseType == null)
        {
            return Result.Failure<PauseTypeDto>("Mola türü bulunamadı.");
        }

        return Result.Success(pauseType);
    }
}

// GetPauseTypeEndpoint.cs
public class GetPauseTypeEndpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapGet("/api/v1/pausetypes/{id:guid}", async (Guid id, IMediator mediator) =>
        {
            var result = await mediator.Send(new GetPauseTypeQuery(id));
            return result.IsSuccess ? Results.Ok(result) : Results.NotFound(result);
        })
        .WithTags("PauseTypes")
        .RequireAuthorization();
    }
}
```

### Infrastructure Implementation

```csharp
// PauseTypeEntityConfiguration.cs
public class PauseTypeEntityConfiguration : IEntityTypeConfiguration<PauseType>
{
    public void Configure(EntityTypeBuilder<PauseType> builder)
    {
        builder.ToTable("PauseType", "PauseTypes");

        builder.HasKey(x => x.Id);

        builder.Property(x => x.Name)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(x => x.MaxDuration)
            .IsRequired();

        builder.Property(x => x.Active)
            .IsRequired()
            .HasDefaultValue(true);

        builder.HasIndex(x => x.Name)
            .IsUnique()
            .HasDatabaseName("UK_PauseType_Name");

        // Base entity konfigürasyonu
        builder.Property(x => x.InsertDate)
            .IsRequired();

        builder.Property(x => x.History)
            .HasColumnType("nvarchar(max)");
    }
}
```

### API Endpoints

```
GET    /api/v1/pausetypes           - Tüm mola türlerini listele
POST   /api/v1/pausetypes           - Yeni mola türü oluştur
GET    /api/v1/pausetypes/{id}      - Belirli mola türünü getir
PUT    /api/v1/pausetypes/{id}      - Mola türünü güncelle
DELETE /api/v1/pausetypes/{id}      - Mola türünü sil
GET    /api/v1/pausetypes/active    - Sadece aktif mola türlerini listele
```

### Business Rules

1. **Benzersizlik**: Aynı isimde birden fazla mola türü oluşturulamaz
2. **Maksimum Süre**: Maksimum süre 0'dan büyük ve 8 saatten (28800 saniye) küçük olmalıdır
3. **Aktivasyon**: Pasif mola türleri kullanıcı arayüzünde gösterilmez
4. **Silme**: Kullanımda olan mola türleri silinemez (soft delete uygulanır)
5. **Güncelleme**: Mola türü güncellendiğinde history alanına değişiklik kaydedilir

### Integration Points

1. **Users Module**: Çalışanların mola alırken hangi tür mola aldığını kaydetmek için
2. **Reporting Module**: Mola analiz raporları için
3. **Notifications Module**: Maksimum süre aşıldığında uyarı göndermek için

### Test Strategy

1. **Unit Tests**: Command/Query handler'lar için
2. **Integration Tests**: Database işlemleri için
3. **Validation Tests**: Business rule validasyonları için

### Performance Considerations

- Aktif mola türleri cache'lenir
- Database index'leri performans için optimize edilir
- Soft delete uygulanır

### Security Considerations

- Sadece yetkilendirilmiş kullanıcılar mola türü yönetimi yapabilir
- Audit logging ile tüm değişiklikler takip edilir
- Input validation ile güvenlik açıkları önlenir

## Sonuç

Bu RFC, MasterCRM sisteminde mola türü yönetiminin nasıl implement edileceğini detaylı olarak açıklar. Modüler yapı, CQRS pattern ve clean architecture prensiplerine uygun olarak tasarlanmıştır. Sistem, gelecekte ek özellikler (mola onay süreci, departman bazlı mola türleri, vb.) için genişletilebilir yapıdadır.