import {
  ApiOutlined,
  CheckOutlined,
  DeleteOutlined,
  FileSyncOutlined,
  FormOutlined,
  StopOutlined,
  WarningOutlined,
} from "@ant-design/icons";
import { Col, Modal, Table, Tag, Tooltip, Typography } from "antd";
import endPoints from "../EndPoints";
import { useQueryClient } from "react-query";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";
import { useGetAutoDialers } from "../ServerSideStates";
import {
  AddAutoDialerToArchive,
  cancelAutoDialer,
  completeAutoDialer,
  deleteAutoDialer,
  startAutoDialer,
} from "../Services";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import { hanldleSetAutoDialerFilter } from "../ClientSideStates";
import dayjs from "dayjs";
import { determineAutoDialerStatus } from "@/helpers/AutoDialer";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { commonRoutePrefix } from "@/routes/Prefix";
const ListItems = () => {
  const { filter } = useSelector((state: RootState) => state.autoDialer);
  const autoDialer = useGetAutoDialers(filter);
  const queryClient = useQueryClient();
  const dispatch = useDispatch();
  const { t } = useTranslation();
  const { Text } = Typography;
  const navigate = useNavigate();

  const columns = [
    {
      title: t("autoDialer.list.name"),
      dataIndex: "Name",
      key: "name",
      width: "20%",
      render: (value: string, record: any) => {
        return (
          <div className=" !flex gap-1 items-center">
            <Tooltip
              title={determineAutoDialerStatus("value", record?.Status, t)}
            >
              <div
                className={`
                        w-[12px] h-[12px] ${determineAutoDialerStatus(
                          "color",
                          record?.Status,
                          t
                        )} transition-all duration-300 
                      group-hover:w-[90px] group-hover:h-[20px] flex items-center justify-center overflow-hidden
                        `}
              ></div>
            </Tooltip>
            <Text className="!text-xs">{value}</Text>
          </div>
        );
      },
      sorter: (a: any, b: any) => {
        return a.Name.localeCompare(b.Name);
      },
    },
    {
      title: t("autoDialer.list.queueNumber"),
      dataIndex: "QueueNumber",
      key: "QueueNumber",
      render: (value: string) => {
        return (
          <>
            <Text className="!text-xs">{value}</Text>
          </>
        );
      },
    },

    {
      title: t("autoDialer.list.startDate"),
      dataIndex: "StartDate",
      key: "StartDate",
      render: (value: string) => {
        return (
          <>
            <Text className="!text-xs">
              {dayjs(value).format("YYYY-MM-DD HH:mm")}
            </Text>
          </>
        );
      },
    },
    {
      title: `${t("autoDialer.list.done")} / ${t("autoDialer.list.total")}`,

      render: (_: string, record: any) => {
        return (
          <>
            <Tag>
              {record?.DoneCount || 0} / {record?.TotalCount || 0}
            </Tag>
          </>
        );
      },
    },

    {
      title: "",
      dataIndex: "edit",
      key: "edit",
      width: "8%",
      render: (key: any, record: any) => (
        <Col className="!flex gap-2 justify-end !pr-6">
          {record?.Status === 0 && (
            <>
              <Tooltip title={t("autoDialer.list.edit")}>
                <FormOutlined
                  className="!text-[#0096d1] !text-sm"
                  onClick={async (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    queryClient.resetQueries({
                      queryKey: endPoints.getAutoDialerDetails,
                      exact: false,
                    });
                    navigate(`${commonRoutePrefix}/edit-auto-dialer/${record.Id}`);
                  }}
                />
              </Tooltip>
              <Tooltip title={t("autoDialer.list.delete")}>
                <DeleteOutlined
                  className="!text-[#9da3af] !text-sm"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    confirm(record);
                  }}
                />
              </Tooltip>
              <Tooltip title={t("autoDialer.list.start")}>
              <ApiOutlined
                className="!text-[#52c41a] !text-sm"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  changeStatusConfirm("start", record);
                }}
              />
            </Tooltip>
            </>
          )}

          {record?.Status === 1 && (
            <Tooltip title={t("autoDialer.list.cancel")}>
              <StopOutlined
                className="!text-red-500 !text-sm"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  changeStatusConfirm("cancel", record);
                }}
              />
            </Tooltip>
          )}

          
          {(record.Status === 2||record.Status ===3 )&& (
            <Tooltip title={t("autoDialer.list.addToArchive")}>
              <FileSyncOutlined
                className="!text-[#faad14] !text-sm"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  changeStatusConfirm("archive", record);
                }}
              />
            </Tooltip>
          )}
        </Col>
      ),
    },
  ];

  const changeStatusConfirm = (
    type: "start" | "cancel" | "complete" | "archive",
    record: any
  ) => {
    let content;
    if (type === "start") {
      content = t("autoDialer.list.startStatusDesc");
    } else if (type === "cancel") {
      content = t("autoDialer.list.cancelStatusDesc");
    } else if (type === "complete") {
      content = t("autoDialer.list.completeStatusDesc");
    } else if (type === "archive") {
      content = t("autoDialer.list.archiveStatusDesc");
    }
    Modal.confirm({
      title: t("autoDialer.list.warning"),
      icon: null,
      content: content,
      okText: t("autoDialer.list.ok"),
      cancelText: t("autoDialer.list.cancel"),
      onOk: async () => {
        try {
          if (type === "start") {
            await startAutoDialer(record);
          } else if (type === "cancel") {
            await cancelAutoDialer(record);
          } else if (type === "complete") {
            await completeAutoDialer(record);
          } else if (type === "archive") {
            await AddAutoDialerToArchive(record);
          }
          openNotificationWithIcon("success", t("form.transactionSuccessful"));
          queryClient.resetQueries({
            queryKey: endPoints.getAutoDialerListFilter,
            exact: false,
          });
        } catch (error: any) {
          showErrorCatching(error, null, false, t);
        }
      },
    });
  };

  const confirm = (record: any) => {
    Modal.confirm({
      title: t("autoDialer.list.warning"),
      icon: null,
      content: t("autoDialer.list.deleteModalDesc"),
      okText: t("autoDialer.list.delete"),
      cancelText: t("autoDialer.list.cancel"),
      onOk: async () => {
        try {
          await deleteAutoDialer(record);
          openNotificationWithIcon("success", t("form.transactionSuccessful"));
          queryClient.resetQueries({
            queryKey: endPoints.getAutoDialerListFilter,
            exact: false,
          });
        } catch (error: any) {
          showErrorCatching(error, null, false, t);
        }
      },
    });
  };
  const handleChangePagination = (pageNum: number, pageSize: number) => {
    let newFilter = { ...filter, PageNumber: pageNum, PageSize: pageSize };
    dispatch(hanldleSetAutoDialerFilter({ filter: newFilter }));
  };
  return (
    <>
      <Table
        columns={columns}
        dataSource={autoDialer?.data?.Value}
        loading={autoDialer.isLoading || autoDialer.isFetching}
        onRow={(record) => {
          return {
            onClick: async (event) => {
              if (record?.Status ===0) {
                queryClient.resetQueries({
                  queryKey: endPoints.getAutoDialerDetails,
                  exact: false,
                });
                navigate(`${commonRoutePrefix}/edit-auto-dialer/${record.Id}`);
              }
            },
          };
        }}
        pagination={{
          position: ["bottomRight"],
          className: "!px-0",
          onChange: handleChangePagination,
          total: autoDialer.data?.FilteredCount || 0,
          current: autoDialer.data?.PageNumber,
          pageSize: autoDialer.data?.PageSize,
          showLessItems: true,
          size: "small",
          showSizeChanger: true,
          locale: { items_per_page: "" },
          showTotal: (e) => `${e}`,
        }}
        rowKey={"Id"}
      />
    </>
  );
};

export default ListItems;
