import { useGetUserOrRolePermissions } from "@/apps/Admin/Pages/Authority/ServerSideStates";
import { RootState } from "@/store/Reducers";
import { useSelector } from "react-redux";
import { Navigate, Outlet, useLocation } from "react-router-dom";

const ValidateUserPermission = () => {
  const { userInfoes } = useSelector((state: RootState) => state.profile);
  const userPermissions = useGetUserOrRolePermissions({
    userId: userInfoes?.Id,
    IncludeProperties: ["Permission"],
  });
  const location = useLocation();

  return (
    <>
      {(() => {
        if (userPermissions?.data?.Value) {
          const findPermission = userPermissions?.data?.Value?.find(
            (permission: any) =>
              permission?.Url?.toLowerCase() === location.pathname
          );
          if (!findPermission) {
            return <Navigate to="/forbidden" />;
          } else {
            return <Outlet />;
          }
        }
      })()}
    </>
  );
};

export default ValidateUserPermission;
