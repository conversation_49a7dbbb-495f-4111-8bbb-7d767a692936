using Customers.Application.Abstractions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Shared.Application;
using Shared.Infrastructure.Localization;

namespace Customers.Application.Customers.BulkAssignAdvisorList;

public class BulkAssignAdvisorListCommandHandler(
    ICustomersDbContext customersDbContext,
    ILocalizer localizer,
    ILogger<BulkAssignAdvisorListCommandHandler> logger) : IRequestHandler<BulkAssignAdvisorListCommand, Result>
{
    private readonly ICustomersDbContext _customersDbContext = customersDbContext;
    private readonly ILocalizer _localizer = localizer;
    private readonly ILogger<BulkAssignAdvisorListCommandHandler> _logger = logger;

    public async Task<Result> Handle(BulkAssignAdvisorListCommand request, CancellationToken cancellationToken)
    {
        try
        {
            int totalAssignments = 0;
            var customers = await _customersDbContext.Customers
                .Where(x => request.CustomerIds.Contains(x.Id))
                .ToListAsync(cancellationToken);

            var foundCustomerIds = customers.Select(c => c.Id).ToHashSet();
            var missingCustomerIds = request.CustomerIds.Where(id => !foundCustomerIds.Contains(id)).ToList();

            if (missingCustomerIds.Any())
            {
                _logger.LogWarning("Bulk assign advisor failed. Missing Customer IDs: {MissingIds}", string.Join(", ", missingCustomerIds));
                return Result.Failure(_localizer.Get("Customers.BulkAssignAdvisorList.SomeCustomerIdsNotFound"));
            }

            foreach (var customer in customers)
            {
                var advisorIdsToAssign = request.AdvisorIds.ToList();
                customer.AssignAdvisors(advisorIdsToAssign);
                totalAssignments += advisorIdsToAssign.Count;
            }
            await _customersDbContext.SaveChangesAsync(cancellationToken);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Bulk assign advisor list to Customers failed.");
            return Result.Failure(_localizer.Get("Customers.BulkAssignAdvisorList.Failed"));
        }
    }
}
