using Customers.Application.Abstractions;
using Customers.Application.Customers.ImportCustomer;
using Customers.Domain;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Shared.Application;
using Shared.Application.EventBus;
using Shared.Application.EventDbLogger;
using Shared.Infrastructure.Excel;
using Shared.Infrastructure.Localization;



namespace Customers.Application.Customers.ExportCustomer;

public class ExportCustomerQueryHandler : IRequestHandler<ExportCustomerQuery, Result<byte[]>>
{
    private readonly ICustomersDbContext _dbContext;
    private readonly ExcelHelper _excelHelper;
    private readonly ILocalizer _localizer;
    private readonly ILogger<ImportTCustomerCommandHandler> _logger;
    private readonly IWorkContext _workContext;
    private readonly IEventBus _eventBus;

    public ExportCustomerQueryHandler(ICustomersDbContext dbContext, ExcelHelper excelHelper, ILocalizer localizer, ILogger<ImportTCustomerCommandHandler> logger, IWorkContext workContext, IEventBus eventBus)
    {
        _localizer = localizer;
        _dbContext = dbContext;
        _excelHelper = excelHelper;
        _logger = logger;
        _workContext = workContext;
        _eventBus = eventBus;
    }

    public async Task<Result<byte[]>> Handle(ExportCustomerQuery request, CancellationToken cancellationToken)
    {
        IQueryable<Customer> query = _dbContext.Customers.Include(x => x.CustomerSource)
        .Include(x => x.CustomerSource)
        .Include(x => x.CustomerClassifications)
        .ThenInclude(x => x.Classification)
        .AsNoTracking();

        if (request.SelectedIds != null && request.SelectedIds.Any())
        {
            query = query.Where(c => request.SelectedIds.Contains(c.Id));
        }
        else if (request.Filter != null)
        {
            query = ApplyCustomerFilters(query, request.Filter);
        }

        var customers = await query.ToListAsync(cancellationToken);


        if (customers == null || !customers.Any())
        {
            return Result.Failure<byte[]>(Error.NotFound("Customers.NotFound", _localizer.Get("Customers.NotFound")));
        }
        var customerDtos = customers.Select(c => new ExportCustomerExcelDto
        {
            Name = c.Name,
            Surname = c.Surname,
            Email = c.Email,
            Phone = c.Phone,
            PhonePrefix = c.PhonePrefix,
            Type = c.Type.ToString(),
            Status = c.Status?.ToString(),
            Kind = c.Kind?.ToString(),
            Country = c.Country,
            MainLanguage = c.MainLanguage,
            AvailableLanguage = c.AvailableLanguage,
            Description = c.Description,
            TaxOffice = c.TaxOffice,
            TaxNumber = c.TaxNumber,
            IdentificationNumber = c.IdentificationNumber,
            MailBcc = c.MailBcc,
            ClassificationName = c.CustomerClassifications != null
                ? string.Join(", ",
                    c.CustomerClassifications
                    .Where(x => x.Classification != null)
                    .Select(x => x.Classification.Name))
                : string.Empty,
            CustomerSourceName = c.CustomerSource?.Name
        }).ToList();

        var file = _excelHelper.ExportToExcel(customerDtos, request.SheetName);
        _logger.LogInformation(
            "Export requested. Module: {Module}, TotalExported: {Total}, Filter: {@Filter}, UserId: {UserId}, TimestampUtc: {@Timestamp}",
            "Customer",
            customers.Count,
            request.Filter,
            _workContext.UserId,
            DateTime.UtcNow
        );

        await _eventBus.PublishAsync(new ExportAuditLoggedEvent(
            _workContext.UserId,
            "Customer",
            customers.Count,
            request.Filter != null,
            request.SelectedIds?.Count,
            $"{request.SheetName}_{DateTime.UtcNow:yyyyMMdd_HHmm}.xlsx",
            request.Filter,
            "Export"
        ), cancellationToken);
        return Result.Success(file);

    }


    private IQueryable<Customer> ApplyCustomerFilters(IQueryable<Customer> query, CustomerFilterRequest filter)
    {
        if (!string.IsNullOrWhiteSpace(filter.Name))
            query = query.Where(c => c.Name.Contains(filter.Name));

        if (!string.IsNullOrWhiteSpace(filter.Surname))
            query = query.Where(c => c.Surname != null && c.Surname.Contains(filter.Surname));

        if (!string.IsNullOrWhiteSpace(filter.Email))
            query = query.Where(c => c.Email != null && c.Email.Contains(filter.Email));

        if (!string.IsNullOrWhiteSpace(filter.Phone))
            query = query.Where(c => c.Phone.Contains(filter.Phone));

        if (!string.IsNullOrWhiteSpace(filter.TaxOffice))
            query = query.Where(c => c.TaxOffice != null && c.TaxOffice.Contains(filter.TaxOffice));

        if (!string.IsNullOrWhiteSpace(filter.TaxNumber))
            query = query.Where(c => c.TaxNumber != null && c.TaxNumber.Contains(filter.TaxNumber));

        if (!string.IsNullOrWhiteSpace(filter.IdentificationNumber))
            query = query.Where(c => c.IdentificationNumber != null && c.IdentificationNumber.Contains(filter.IdentificationNumber));

        if (!string.IsNullOrWhiteSpace(filter.Country))
            query = query.Where(c => c.Country != null && c.Country.Contains(filter.Country));

        if (!string.IsNullOrWhiteSpace(filter.MainLanguage))
            query = query.Where(c => c.MainLanguage != null && c.MainLanguage.Contains(filter.MainLanguage));

        if (!string.IsNullOrWhiteSpace(filter.AvailableLanguage))
            query = query.Where(c => c.AvailableLanguage != null && c.AvailableLanguage.Contains(filter.AvailableLanguage));

        if (!string.IsNullOrWhiteSpace(filter.Description))
            query = query.Where(c => c.Description != null && c.Description.Contains(filter.Description));

        if (!string.IsNullOrWhiteSpace(filter.MailBcc))
            query = query.Where(c => c.MailBcc != null && c.MailBcc.Contains(filter.MailBcc));

        if (filter.Type.HasValue)
            query = query.Where(c => c.Type == filter.Type.Value);

        if (filter.Kind.HasValue)
            query = query.Where(c => c.Kind == filter.Kind.Value);

        if (filter.Status.HasValue)
            query = query.Where(c => c.Status == filter.Status.Value);

        if (filter.ProfessionId.HasValue)
            query = query.Where(c => c.ProfessionId == filter.ProfessionId.Value);

        if (filter.SectorId.HasValue)
            query = query.Where(c => c.SectorId == filter.SectorId.Value);

        if (filter.CustomerSourceId.HasValue)
            query = query.Where(c => c.CustomerSourceId == filter.CustomerSourceId.Value);

        if (filter.AdvisorId.HasValue)
            query = query.Where(c => c.AdvisorIds != null && c.AdvisorIds.Contains(filter.AdvisorId.Value));

        if (filter.TopCustomerId.HasValue)
            query = query.Where(c => c.TopCustomerId == filter.TopCustomerId.Value);

        if (filter.ClassificationIds != null && filter.ClassificationIds.Any())
            query = query.Where(c => c.CustomerClassifications.Any(cc => filter.ClassificationIds.Contains(cc.ClassificationId)));

        return query;
    }

}
