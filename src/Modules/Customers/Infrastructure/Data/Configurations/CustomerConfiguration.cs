using System.Text.Json;
using Customers.Domain;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Customers.Infrastructure.Data.Configurations;

public class CustomerConfiguration : IEntityTypeConfiguration<Customer>
{
    public void Configure(EntityTypeBuilder<Customer> builder)
    {
        builder.ToTable("Customer");

        builder.HasKey(x => x.Id);

        builder.Property(x => x.Name)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(x => x.Surname)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(x => x.Email)
            .IsRequired()
            .HasMaxLength(255);

        builder.Property(x => x.Phone)
            .IsRequired()
            .HasMaxLength(20);

        builder.Property(x => x.Type)
            .IsRequired();

        builder.Property(x => x.CustomerSourceId)
            .IsRequired(false);

        builder.Property(x => x.NotificationWayId)
            .IsRequired(false);

        builder.Property(x => x.AttributeData)
            .HasColumnType("nvarchar(max)");

        builder.HasMany(x => x.Contacts)
            .WithOne(x => x.Customer)
            .HasForeignKey(x => x.CustomerId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(x => x.AdvisorHistory)
            .WithOne(x => x.Customer)
            .HasForeignKey(x => x.CustomerId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(x => x.CustomerClassifications);
        //.WithOne(x => x.Customer)
        //.HasForeignKey(x => x.CustomerId)
        //.OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(x => x.SubCustomers)
            .WithOne(x => x.TopCustomer)
            .HasForeignKey(x => x.TopCustomerId)
            .OnDelete(DeleteBehavior.NoAction);

        builder.HasIndex(x => x.Email)
            .IsUnique();

        builder.HasOne(x => x.NotificationWay)
            .WithMany()
            .HasForeignKey(x => x.NotificationWayId)
            .OnDelete(DeleteBehavior.SetNull);

        builder.Property(e => e.AttributeData).HasColumnType("nvarchar(max)").HasConversion(
            v => JsonSerializer.Serialize(v, (JsonSerializerOptions)null),
            v => JsonSerializer.Deserialize<Dictionary<string, string>>(v, (JsonSerializerOptions)null)
        );


        // JSON kolon yapılandırması
        builder.Property(e => e.AdvisorIds)
            .HasColumnType("nvarchar(max)")
            .HasConversion(
                v => JsonSerializer.Serialize(v, new JsonSerializerOptions()),
                v => JsonSerializer.Deserialize<List<Guid>>(v, new JsonSerializerOptions()) ?? new List<Guid>());

        // Profession ile ilişki
        builder.HasOne(c => c.Profession)
            .WithMany()
            .HasForeignKey(c => c.ProfessionId)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.SetNull);
    }
}
