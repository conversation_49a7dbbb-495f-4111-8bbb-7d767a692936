using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;
using Users.Application.Abstractions;

namespace Users.Application.Pauses.GetPauseList;

public class GetPauseListQueryHandler(
    IUserDbContext dbcontext,
    IWorkContext workContext
) : IRequestHandler<GetPauseListQuery, PagedResult<PauseListResponse>>
{
    private readonly IUserDbContext _dbcontext = dbcontext;
    private readonly IWorkContext _workContext = workContext;

    public async Task<PagedResult<PauseListResponse>> Handle(GetPauseListQuery request, CancellationToken cancellationToken)
    {
        var query = _dbcontext.Pause
            .Include(p => p.User)
            .Include(p => p.Type)
            .AsQueryable();

        if (!_workContext.HasRole("Admin"))
        {
            var userId = _workContext.UserId;
            query = query.Where(p => p.UserId == userId);
        }
        if (request.UserId.HasValue)
        {
            query = query.Where(p => p.UserId == request.UserId.Value);
        }
        if (request.Status.HasValue)
        {
            query = query.Where(p => p.Status == request.Status.Value);
        }
        if (request.TypeId.HasValue)
        {
            query = query.Where(p => p.TypeId == request.TypeId.Value);
        }
        if (request.FromDate.HasValue)
        {
            query = query.Where(p => p.StartDateTime.Date >= request.FromDate.Value.Date);
        }
        if (request.ToDate.HasValue)
        {
            query = query.Where(p => p.StartDateTime.Date <= request.ToDate.Value.Date);
        }
        if (request.DurationMin.HasValue)
        {
            query = query.Where(p => p.Duration >= request.DurationMin.Value);
        }
        if (request.DurationMax.HasValue)
        {
            query = query.Where(p => p.Duration <= request.DurationMax.Value);
        }
        var filteredCount = await query.CountAsync(cancellationToken);
        var totalCount = await _dbcontext.Pause.CountAsync(cancellationToken);
        var items = await query
            .OrderByDescending(p => p.StartDateTime)
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .Select(p => new PauseListResponse(
                p.Id,
                $"{p.User.Name} {p.User.Surname}",
                p.Type.Name,
                p.StartDateTime,
                p.EndDateTime,
                p.ActualStartTime,
                p.PersonalDescription,
                p.AdministratorDescription,
                p.Duration,
                p.Status
            ))
            .ToListAsync(cancellationToken);

        return new PagedResult<PauseListResponse>(items)
        {
            PageNumber = request.PageNumber,
            PageSize = request.PageSize,
            FilteredCount = filteredCount,
            Count = totalCount,
        };
    }
}
