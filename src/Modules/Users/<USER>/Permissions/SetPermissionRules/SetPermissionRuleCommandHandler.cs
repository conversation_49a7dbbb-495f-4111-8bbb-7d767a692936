using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;
using Users.Application.Abstractions;
using Users.Domain.Auth;

namespace Users.Application.Permissions.SetPermissionRules;

public class SetPermissionRuleCommandHandler(
    IUserDbContext context
    ) : IRequestHandler<SetPermissionRuleCommand, Result>
{
    private readonly IUserDbContext _context = context;

    public async Task<Result> Handle(SetPermissionRuleCommand request, CancellationToken cancellationToken)
    {
        //await _context.PermissionRule.Where().ExecuteDeleteAsync()
        var permissionRules = request.SetPermissionRules.Select(x => new PermissionRule
        {
            PermissionId = x.PermissionId,
            UserId = x.UserId,
            RoleId = x.RoleId
        }).ToList();
        _context.PermissionRule.AddRange(permissionRules);
        await _context.SaveChangesAsync(cancellationToken);
        return Result.Success();
    }
}
