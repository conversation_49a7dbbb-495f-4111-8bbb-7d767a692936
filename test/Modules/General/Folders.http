### <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
@baseUrl = {{$dotenv domain}}/api/v1/users
@email = {{$dotenv email}}
@password = {{$dotenv password}}

###

# @name login
POST {{$dotenv domain}}/api/v1/general/account/login
Content-Type: application/json

{
    "Email": "{{email}}",
    "Password": "{{password}}"
}

###

@token = {{login.response.body.AccessToken}}

### GetTotalProduct
GET {{baseUrl}}/account/userinfo
Authorization: Bearer {{token}}